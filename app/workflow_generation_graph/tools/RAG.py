import json
import os

import requests
from pinecone import Pinecone
from strands import tool

PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
pc = Pinecone(api_key=PINECONE_API_KEY)
index = pc.Index("tool-embeddings")
banned_mcp_ids = json.load(open("banned_mcp_ids.json"))
url = "https://app-dev.rapidinnovation.dev/api/v1/embedding/single"
embedding_key = "lzWHZgmWuT3cV4wtuOVxBvwJflIqnUy3m3plwdrf"
headers = {
    "X-Embedding-Service-Key": embedding_key,
    "Content-Type": "application/json",
}


@tool(
    name="RAG_search",
    description="Function take a description query and return the top k nodes which are semantically similar to the description. it return the list of dictionary which contains the type and description of the node. The default value of k is 10.",
)
def RAG_search(query: str, k: int = 10) -> list:
    response = requests.post(url, headers=headers, json={"text": query})
    data = response.json()
    embedding = data["embedding"]
    results = index.query(
        vector=embedding,
        top_k=k,
        include_metadata=True,
        filter={"$or": [{"type": {"$ne": "mcp"}}, {"id": {"$nin": banned_mcp_ids}}]},
    )
    results = results["matches"]
    output = [data["metadata"] for data in results]
    return output
