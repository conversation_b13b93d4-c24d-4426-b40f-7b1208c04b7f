import json
from typing import Any, Dict

import requests

# Component type exceptions mapping
COMPONENT_TYPE_EXCEPTIONS = {
    "AgenticAI": "agent",
    "LoopNode": "loop",
}

# Default node dimensions
DEFAULT_NODE_DIMENSIONS = {
    "width": 208,
    "height": 194,
    "position": {"x": 1760, "y": 1960},
}


def fulfill_component(
    node_info: dict,
    api_url: str = "https://app-dev.rapidinnovation.dev/api/v1/components",
) -> Dict[str, Any]:
    """
    Generate a complete workflow node structure for the given component.

    Args:
        node_info (dict): Node information containing OriginalType, node_id, etc.
        api_url (str): API endpoint URL for components

    Returns:
        dict: Complete node structure with definition from API or error dict
    """
    try:
        response = requests.get(api_url)
        response.raise_for_status()
        components_data = response.json()

        # Find component definition across all categories
        component_def = _find_component_definition(
            components_data, node_info["OriginalType"]
        )

        if not component_def:
            return _create_component_error_response(
                components_data, node_info["OriginalType"]
            )

        # Create and configure node structure
        node_structure = _create_base_node_structure(node_info, component_def)
        _apply_node_configuration(node_structure, node_info)
        _apply_default_input_values(node_structure, node_info)

        return node_structure

    except requests.exceptions.RequestException as e:
        return {"error": f"API request failed: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"Failed to parse API response: {str(e)}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


def _find_component_definition(components_data: dict, node_name: str) -> dict:
    """Find component definition by searching through all categories."""
    for _, components in components_data.items():
        if node_name in components:
            return components[node_name]
    return None


def _create_component_error_response(components_data: dict, node_name: str) -> dict:
    """Create error response with available components list."""
    available_components = []
    for _, components in components_data.items():
        available_components.extend(components.keys())

    error_msg = f"Component '{node_name}' not found in API response.\n"
    error_msg += f"Available components ({len(available_components)}):\n"
    error_msg += "\n".join(
        [f"  - {comp}" for comp in sorted(available_components)[:20]]
    )

    if len(available_components) > 20:
        error_msg += f"\n  ... and {len(available_components) - 20} more"

    return {"error": error_msg}


def _create_base_node_structure(node_info: dict, component_def: dict) -> dict:
    """Create the base node structure with component definition."""
    node_name = node_info["OriginalType"]

    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": DEFAULT_NODE_DIMENSIONS["position"].copy(),
        "data": {
            "label": component_def.get("display_name", node_name),
            "type": COMPONENT_TYPE_EXCEPTIONS.get(node_name, "component"),
            "originalType": node_name,
            "definition": component_def,
            "config": {},
        },
        "width": DEFAULT_NODE_DIMENSIONS["width"],
        "height": DEFAULT_NODE_DIMENSIONS["height"],
        "selected": False,
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_node_configuration(node_structure: dict, node_info: dict) -> None:
    """Apply node-specific configuration from node_info."""
    node_structure["position"] = node_info["position"]
    node_structure["data"]["label"] = node_info["label"]
    node_structure["data"]["config"] = node_info.get("parameters", {})
    node_structure["width"] = node_info["dimension"]["width"]
    node_structure["height"] = node_info["dimension"]["height"]


def _apply_default_input_values(node_structure: dict, node_info: dict) -> None:
    """Apply default values for inputs that have values but aren't in parameters."""
    inputs = node_structure["data"]["definition"]["inputs"]
    parameters = node_info.get("parameters", {})

    for input_item in inputs:
        if input_item["value"] and input_item["name"] not in parameters:
            node_structure["data"]["config"][input_item["name"]] = input_item["value"]


def fulfill_mcp(
    node_info: dict,
    api_detail_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/",
) -> Dict[str, Any]:
    """
    Fetches an MCP from the marketplace by ID and constructs a WorkflowNode.

    Args:
        node_info (dict): Node information containing mcp_id, tool_name, etc.
        api_detail_url (str): Base API URL for MCP marketplace

    Returns:
        dict: Complete MCP node structure
    """
    mcp_id = node_info["mcp_id"]
    tool_name = node_info["tool_name"]

    # Fetch MCP data from API
    mcp_data = _fetch_mcp_data(api_detail_url + mcp_id, mcp_id)

    # Extract tool configuration
    tool_config = _extract_tool_config(mcp_data, tool_name)

    # Build inputs and outputs
    inputs = _build_mcp_inputs(tool_config["props"], tool_config["required"])
    outputs = _build_mcp_outputs(
        tool_config["outputs_schema"], tool_config["has_output"]
    )

    # Create definition and node structure
    definition = _create_mcp_definition(
        mcp_data, mcp_id, tool_name, tool_config["tool"], inputs, outputs
    )
    node = _create_mcp_node_structure(node_info, definition)

    # Apply configuration and default values
    _apply_mcp_configuration(node, node_info, mcp_data)

    return node


def _fetch_mcp_data(api_url: str, mcp_id: str) -> dict:
    """Fetch MCP data from the marketplace API."""
    response = requests.get(api_url, headers={"Content-Type": "application/json"})
    response.raise_for_status()

    data = response.json()
    mcp = data.get("mcp") or data.get("data") or data

    if not mcp:
        raise ValueError(f"No MCP with id={mcp_id} found in marketplace API")

    return mcp


def _extract_tool_config(mcp_data: dict, tool_name: str) -> dict:
    """Extract tool configuration from MCP data."""
    tools = mcp_data.get("mcp_tools_config", {}).get("tools", [])

    if not tools:
        return {
            "tool": None,
            "props": {},
            "required": set(),
            "has_output": False,
            "outputs_schema": {},
        }

    tool = next((tool for tool in tools if tool["name"] == tool_name), None)
    if not tool:
        return {
            "tool": None,
            "props": {},
            "required": set(),
            "has_output": False,
            "outputs_schema": {},
        }

    props = tool.get("input_schema", {}).get("properties", {})
    required = set(tool.get("input_schema", {}).get("required", []))
    has_output = bool(tool.get("output_schema"))
    outputs_schema = (
        tool.get("output_schema", {}).get("properties", {}) if has_output else {}
    )

    return {
        "tool": tool,
        "props": props,
        "required": required,
        "has_output": has_output,
        "outputs_schema": outputs_schema,
    }


def _build_mcp_inputs(props: dict, required: set) -> list:
    """Build input configuration for MCP node."""
    inputs = []

    for name, schema in props.items():
        input_type = schema.get("type")
        itype = (
            "array"
            if input_type == "array" or "items" in schema
            else input_type or "string"
        )

        inputs.append(
            {
                "name": name,
                "display_name": name[0].upper() + name[1:].replace("_", " "),
                "info": "",
                "input_type": itype,
                "input_types": [itype, "Any"],
                "required": name in required,
                "is_handle": True,
                "is_list": itype == "array",
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "validation": {},
            }
        )

    return inputs


def _build_mcp_outputs(outputs_schema: dict, has_output: bool) -> list:
    """Build output configuration for MCP node."""
    outputs = []

    if has_output:
        for name, schema in outputs_schema.items():
            outputs.append(
                {
                    "name": name,
                    "display_name": schema.get("title", name),
                    "output_type": schema.get("type", "Any"),
                }
            )
    else:
        outputs.append(
            {"name": "result", "display_name": "Result", "output_type": "Any"}
        )

    return outputs


def _create_mcp_definition(
    mcp_data: dict, mcp_id: str, tool_name: str, tool: dict, inputs: list, outputs: list
) -> dict:
    """Create MCP definition structure."""
    logo = mcp_data.get("logo", "")
    icon = logo.split("/")[-1].split(".")[0].capitalize() if logo else ""

    return {
        "name": mcp_id,
        "display_name": mcp_data.get("name", mcp_id),
        "description": mcp_data.get("description", ""),
        "category": mcp_data.get("category", ""),
        "icon": icon,
        "beta": False,
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "MCP",
        "logo": logo,
        "mcp_info": {
            "server_id": mcp_id,
            "server_path": "",
            "tool_name": tool_name,
            "input_schema": tool.get("input_schema", {}) if tool else {},
            "output_schema": tool.get("output_schema", {}) if tool else {},
        },
    }


def _create_mcp_node_structure(node_info: dict, definition: dict) -> dict:
    """Create the base MCP node structure."""
    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_info["position"],
        "data": {
            "label": node_info["label"],
            "type": "mcp",
            "originalType": node_info["OriginalType"],
            "definition": definition,
            "config": {},
            "oauthConnectionState": {},
        },
        "width": 388,
        "height": 519,
        "selected": True,
        "positionAbsolute": node_info["position"],
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_mcp_configuration(node: dict, node_info: dict, mcp_data: dict) -> None:
    """Apply MCP-specific configuration to the node."""
    # Apply basic configuration
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]

    # Add integrations if available
    if mcp_data.get("integrations"):
        node["data"]["definition"]["integrations"] = mcp_data["integrations"]

    # Apply default input values
    inputs = node["data"]["definition"]["inputs"]
    parameters = node_info.get("parameters", {})

    for input_item in inputs:
        if input_item["value"] and input_item["name"] not in parameters:
            node["data"]["config"][input_item["name"]] = input_item["value"]


def fulfill_workflow(
    node_info: dict,
    api_base_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/",
) -> Dict[str, Any]:
    """
    Fetches a workflow from the marketplace and constructs a WorkflowNode.

    Args:
        node_info (dict): Node information containing workflow_id, label, etc.
        api_base_url (str): Base API URL for workflow marketplace

    Returns:
        dict: Complete workflow node structure
    """
    workflow_id = node_info["workflow_id"]

    try:
        # Fetch workflow data from API
        workflow_data = _fetch_workflow_data(api_base_url, workflow_id)

        # Build inputs and outputs
        inputs = _build_workflow_inputs(workflow_data.get("start_nodes", []))
        outputs = _build_workflow_outputs()

        # Create workflow info and definition
        workflow_info = _create_workflow_info(workflow_data, workflow_id)
        definition = _create_workflow_definition(
            workflow_id, node_info["label"], inputs, outputs, workflow_info
        )

        # Create and configure node
        node = _create_workflow_node_structure(node_info, definition)
        _apply_workflow_configuration(node, node_info)

        return node

    except requests.RequestException as e:
        raise Exception(f"Failed to fetch workflow data from marketplace: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response: {str(e)}")
    except Exception as e:
        raise Exception(f"Error generating workflow node: {str(e)}")


def _fetch_workflow_data(api_base_url: str, workflow_id: str) -> dict:
    """Fetch workflow data from the marketplace API."""
    api_url = f"{api_base_url}{workflow_id}"
    headers = {"Content-Type": "application/json"}

    response = requests.get(api_url, headers=headers)

    # Handle specific error cases
    if response.status_code == 404:
        raise Exception(f"Workflow not found in marketplace: {workflow_id}")
    elif response.status_code == 500:
        raise Exception("Internal server error - try again later")
    elif response.status_code != 200:
        raise Exception(f"API returned status code: {response.status_code}")

    response.raise_for_status()
    api_response = response.json()

    if not api_response:
        raise ValueError("Empty response from marketplace API")

    # Extract workflow data from various response formats
    return _extract_workflow_from_response(api_response)


def _extract_workflow_from_response(api_response: dict) -> dict:
    """Extract workflow data from API response with flexible format handling."""
    if not isinstance(api_response, dict):
        raise ValueError("Unexpected response format from marketplace API")

    # Check for common response wrapper formats
    workflow = (
        api_response.get("workflow")
        or api_response.get("data")
        or api_response.get("result")
        or api_response
    )

    if not workflow:
        raise ValueError("No workflow data found in response")

    return workflow


def _build_workflow_inputs(start_nodes: list) -> list:
    """Build input configuration for workflow node."""
    inputs = []

    for start_node in start_nodes:
        is_handle_value = start_node.get("type") == "handle"
        field_name = start_node.get("field", "input_data")

        input_item = {
            "name": field_name,
            "display_name": field_name.replace("_", " ").title(),
            "info": f"Input field: {field_name}",
            "input_type": "string",
            "required": True,
            "is_handle": is_handle_value,
            "is_list": False,
            "real_time_refresh": False,
            "advanced": False,
            "value": None,
            "options": None,
            "visibility_rules": None,
            "visibility_logic": "OR",
            "requirement_rules": None,
            "requirement_logic": "OR",
            "transition_id": start_node.get("transition_id"),
        }
        inputs.append(input_item)

    return inputs


def _build_workflow_outputs() -> list:
    """Build standard workflow outputs."""
    return [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]


def _create_workflow_info(workflow_data: dict, workflow_id: str) -> dict:
    """Create workflow info structure."""
    return {
        "id": workflow_id,
        "name": workflow_data.get("name", ""),
        "description": workflow_data.get("description", ""),
        "workflow_url": workflow_data.get("workflow_url"),
        "builder_url": workflow_data.get("builder_url"),
        "start_nodes": workflow_data.get("start_nodes", []),
        "owner_id": workflow_data.get("owner_id"),
        "user_ids": (
            [workflow_data.get("owner_id")] if workflow_data.get("owner_id") else []
        ),
        "owner_type": "user",
        "workflow_template_id": None,
        "template_owner_id": None,
        "is_imported": False,
        "version": workflow_data.get("version", "1.0.0"),
        "visibility": workflow_data.get("visibility", "private").lower(),
        "category": workflow_data.get("category"),
        "tags": workflow_data.get("tags"),
        "status": workflow_data.get("status", "active"),
        "is_changes_marketplace": False,
        "is_customizable": True,
        "auto_version_on_update": False,
        "created_at": workflow_data.get("created_at"),
        "updated_at": workflow_data.get("updated_at"),
        "available_nodes": workflow_data.get("available_nodes") or [],
        "is_updated": True,
        "source_version_id": workflow_data.get("source_version_id"),
    }


def _create_workflow_definition(
    workflow_id: str, label: str, inputs: list, outputs: list, workflow_info: dict
) -> dict:
    """Create workflow definition structure."""
    return {
        "name": f"workflow-{workflow_id}",
        "display_name": label,
        "description": label,
        "category": "Workflows",
        "icon": "Workflow",
        "beta": False,
        "path": f"workflow.{workflow_id}",
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "Workflow",
        "workflow_info": workflow_info,
    }


def _create_workflow_node_structure(node_info: dict, definition: dict) -> dict:
    """Create the base workflow node structure."""
    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_info["position"],
        "data": {
            "label": node_info["label"],
            "type": "component",
            "originalType": f"workflow-{node_info['workflow_id']}",
            "definition": definition,
            "config": node_info.get("parameters", {}),
        },
        "width": 388,
        "height": 326,
        "selected": False,
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_workflow_configuration(node: dict, node_info: dict) -> None:
    """Apply workflow-specific configuration to the node."""
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]


def _return_node_template(node_info: dict) -> dict:
    """
    Return the appropriate node template based on node type.

    Args:
        node_info (dict): Node information containing type and other details

    Returns:
        dict: Complete node structure based on type

    Raises:
        ValueError: If node type is unknown
    """
    node_type = node_info["type"].lower().strip()

    node_type_handlers = {
        "component": fulfill_component,
        "workflow": fulfill_workflow,
        "mcp": fulfill_mcp,
    }

    handler = node_type_handlers.get(node_type)
    if not handler:
        raise ValueError(f"Unknown node type: {node_type}")

    return handler(node_info=node_info)


def _return_edge_template(edge: dict) -> dict:
    """
    Create edge template with proper formatting.

    Args:
        edge (dict): Edge information containing source, target, handles

    Returns:
        dict: Complete edge structure for React Flow
    """
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "type": "default",
        "selected": False,
    }

    template.update(edge)
    template["id"] = _generate_edge_id(edge)

    return template


def _generate_edge_id(edge: dict) -> str:
    """Generate unique edge ID from edge properties."""
    return (
        f"reactflow__edge{edge['source']}{edge['sourceHandle']}-"
        f"{edge['target']}{edge['targetHandle']}"
    )


def post_processing(workflow: dict) -> dict:
    """
    Post-process workflow data to create complete node and edge structures.

    Args:
        workflow (dict): Raw workflow data with nodes and edges

    Returns:
        dict: Processed workflow with complete node and edge structures
    """
    output = {"nodes": [], "edges": []}
    required_parameters = []

    # Process nodes
    for node in workflow["nodes"]:
        if node["OriginalType"] == "StartNode":
            node["node_id"] = "start-node"

        processed_node = _return_node_template(node)
        output["nodes"].append(processed_node)

        # Track required parameters that need connections
        _track_required_parameters(processed_node, required_parameters)

    # Process edges
    for edge in workflow["edges"]:
        target = edge["target"]
        target_handle = edge["targetHandle"]

        # Remove satisfied requirements
        if (target, target_handle) in required_parameters:
            required_parameters.remove((target, target_handle))

        output["edges"].append(_return_edge_template(edge))

    # Add missing edges for required parameters
    _add_missing_edges(required_parameters, output)

    return output


def _track_required_parameters(node: dict, required_parameters: list) -> None:
    """Track required parameters that need connections."""
    inputs = node["data"]["definition"]["inputs"]
    config = node["data"]["config"]

    for input_item in inputs:
        if (
            input_item["required"]
            and input_item["name"] not in config
            and input_item["is_handle"]
        ):
            required_parameters.append((node["id"], input_item["name"]))


def _add_missing_edges(required_parameters: list, output: dict) -> None:
    """Add edges for required parameters that don't have connections."""
    for target, target_handle in required_parameters:
        edge = {
            "source": "start-node",
            "sourceHandle": "flow",
            "target": target,
            "targetHandle": target_handle,
        }
        output["edges"].append(_return_edge_template(edge))
